--##################################################################################################################

CREATE EXTENSION aws_s3 CASCADE;

--##################################################################################################################
-- CREATE ROLES AND USERS FOR POSTGRES
--##################################################################################################################

-- CREATE READONLY ROLE AND READWRITE ROLE
-- Create the database
CREATE DATABASE valuations;

-- Create roles
CREATE ROLE valuations_readonly;
CREATE ROLE valuations_readwrite;

-- Grant privileges to roles
-- Read-only: SELECT only
GRANT CONNECT ON DATABASE valuations TO valuations_readonly;
GRANT USAGE ON SCHEMA public TO valuations_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO valuations_readonly;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO valuations_readonly;

-- Read-write: SELECT, INSERT, UPDATE, DELETE
GRANT CONNECT ON DATABASE valuations TO valuations_readwrite;
GRANT ALL ON SCHEMA public TO valuations_readwrite;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO valuations_readwrite;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO valuations_readwrite;



-- Grant usage on the aws_s3 and aws_commons schemas
GRANT USAGE ON SCHEMA aws_s3 TO valuations_readwrite;
GRANT USAGE ON SCHEMA aws_commons TO valuations_readwrite;

-- Grant execute on all functions in those schemas
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA aws_s3 TO valuations_readwrite;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA aws_commons TO valuations_readwrite;


-- Create users and assign roles
CREATE USER user_readonly WITH PASSWORD 'valuations_readonly_password';
GRANT valuations_readonly TO user_readonly;

CREATE USER user_readwrite WITH PASSWORD 'readwrite_password';
GRANT valuations_readwrite TO user_readwrite;

-- Optional: Create a user with both roles
CREATE USER ravi_valuations WITH PASSWORD 'ravip1234';
GRANT valuations_readonly TO ravi_valuations;
GRANT valuations_readwrite TO ravi_valuations;

-- Optional: Create a user with both roles
CREATE USER edl_readonly_user WITH PASSWORD 'edlrouser_pass1234';
GRANT valuations_readonly TO edl_readonly_user;

