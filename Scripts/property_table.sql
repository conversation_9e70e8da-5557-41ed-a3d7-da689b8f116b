create table public.property
(
	RECORD_ID NUMERIC(10),
	PROPERTY_ID_LIGHTSTONE NUMERIC(12),
	MATCH_STATUS_FINAL VARCHAR(50),
	EXTRACT_DATE DATE,
	CAPITEC_KEY_1 VARCHAR(20),
	CAPITEC_COLLATERAL_ID VARCHAR(20),
	CAPITEC_TYPE VARCHAR(10),
	CAPITEC_ID_NUMBER VARCHAR(20),
	CAPITEC_CIF_NO VARCHAR(20),
	CAPITEC_BANK_VALUATION NUMERIC(12),
	CAPITEC_SHORT_DESC VARCHAR(100),
	CAPITEC_CURRENCY VARCHAR(5),
	CAPITEC_START_DATE DATE,
	CAPITEC_EXPIRY_DATE DATE,
	CAPITEC_REVIEW_DATE DATE,
	CAPITEC_STATUS VARCHAR(10),
	CAPITEC_DESCRIPTION_1 VARCHAR(100),
	CAPITEC_DESCRIPTION_2 VARCHAR(100),
	CAPITEC_DESCRIPTION_3 VARCHAR(100),
	CAPITEC_MARKET_VALUE NUMERIC(12),
	CAPITEC_CITY_LOCATION VARCHAR(20),
	CAPITEC_ADDRESS_1 VARCHAR(100),
	CAPITEC_ADDRESS_2 VARCHAR(100),
	CAPITEC_OWNER_NAME VARCHAR(100),
	CAPITEC_OWN_ADDRESS_1 VARCHAR(100),
	CAPITEC_OWN_ADDRESS_2 VARCHAR(100),
	PORTION NUMERIC(5),
	ERF_NUMBER NUMERIC(7),
	TOWNSHIP_NAME VARCHAR(100),
	SCHEME_NAME VARCHAR(100),
	SCHEME_UNIT_NUMBER INT,
	LEGAL_DESCRIPTION VARCHAR(300),
	FARM_NAME VARCHAR(50),
	STREET_ADDRESS VARCHAR(100),
	SUBURB VARCHAR(50),
	POSTAL_CODE INT,
	TOWN VARCHAR(50),
	MUNICIPALITY VARCHAR(50),
	PROVINCE VARCHAR(50),
	LONGITUDE DECIMAL(10,8),
	LATTITUDE DECIMAL(10,8),
	PROPERTY_SIZE_SQM NUMERIC(8),
	IS_GARAGE_FLAG NUMERIC(1),
	IS_RESIDENTIAL_FLAG NUMERIC(1),
	TITLE_DEED_NO VARCHAR(50),
	PROPERTIES_ON_TITLE_SHARE NUMERIC(3),
	PURCHASE_PRICE NUMERIC(12),
	PURCHASE_DATE DATE,
	REGISTRATION_DATE DATE,
	PROPERTY_TYPE VARCHAR(1),
	PROPERTY_STOCK_TYPE VARCHAR(6),
	TRANSFEREE_SHARE DECIMAL(10,8),
	NUMBER_OF_OWNERS NUMERIC(3),
	OWNER_IDENTIFIER_NUMBER VARCHAR(50),
	OWNER_NAME VARCHAR(200),
	OWNER_TYPE VARCHAR(30),
	OWNER_PROPERTIES_COUNT NUMERIC(3),
	BOND_INSTITUTION_NAME_1 VARCHAR(50),
	BOND_HOLDER_1 VARCHAR(50),
	BOND_NUMBER_1 VARCHAR(50),
	BOND_VALUE_1 NUMERIC(12),
	BOND_REGISTRATION_DATE_1 DATE,
	BOND_TYPE_1 VARCHAR(20),
	NO_OF_PROPERTIES_IN_BOND_1 NUMERIC(3),
	BOND_STATUS_1 VARCHAR(10),
	BOND_INSTITUTION_NAME_2 VARCHAR(50),
	BOND_HOLDER_2 VARCHAR(50),
	BOND_NUMBER_2 VARCHAR(50),
	BOND_VALUE_2 NUMERIC(12),
	BOND_REGISTRATION_DATE_2 DATE,
	BOND_TYPE_2 VARCHAR(20),
	NO_OF_PROPERTIES_IN_BOND_2 NUMERIC(3),
	BOND_STATUS_2 VARCHAR(10),
	BOND_INSTITUTION_NAME_3 VARCHAR(50),
	BOND_HOLDER_3 VARCHAR(50),
	BOND_NUMBER_3 VARCHAR(50),
	BOND_VALUE_3 NUMERIC(12),
	BOND_REGISTRATION_DATE_3 DATE,
	BOND_TYPE_3 VARCHAR(20),
	NO_OF_PROPERTIES_IN_BOND_3 NUMERIC(3),
	BOND_STATUS_3 VARCHAR(10),
	PROPERTY_RQG NUMERIC(3),
	AIVM_AVM_ESTIMATE_VALUE NUMERIC(12),
	AIVM_AVM_FLAG VARCHAR(5),
	RCV_ML_LATEST_ESTIMATED_VALUE NUMERIC(12),
	RCV_ML_CONFIDENCE_SCORE NUMERIC(4,2),
	RCV_ML_DATE DATE,
	AIVM_AVM_CONFIDENCE_SCORE NUMERIC(4,2),
	AIVM_AVM_SAFETY_SCORE NUMERIC(4,2),
	load_id numeric(100),
	load_date timestamp
);
grant select on public.property to valuations_readonly,valuations_readwrite;
