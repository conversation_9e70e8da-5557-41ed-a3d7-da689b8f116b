create or replace view public.vwproperty as 
select 
record_id,
property_id_lightstone,
match_status_final,
extract_date,
capitec_key_1,
capitec_collateral_id,
capitec_type,
capitec_id_number,
capitec_cif_no,
capitec_bank_valuation,
capitec_short_desc,
capitec_currency,
capitec_start_date,
capitec_expiry_date,
capitec_review_date,
capitec_status,
capitec_description_1,
capitec_description_2,
capitec_description_3,
capitec_market_value,
capitec_city_location,
capitec_address_1,
capitec_address_2,
capitec_owner_name,
capitec_own_address_1,
capitec_own_address_2,
portion,
erf_number,
township_name,
scheme_name,
scheme_unit_number,
legal_description,
farm_name,
street_address,
suburb,
postal_code,
town,
municipality,
province,
longitude,
lattitude,
property_size_sqm,
is_garage_flag,
is_residential_flag,
title_deed_no,
properties_on_title_share,
purchase_price,
purchase_date,
registration_date,
property_type,
property_stock_type,
transferee_share,
number_of_owners,
owner_identifier_number,
owner_name,
owner_type,
owner_properties_count,
bond_institution_name_1,
bond_holder_1,
bond_number_1,
bond_value_1,
bond_registration_date_1,
bond_type_1,
no_of_properties_in_bond_1,
bond_status_1,
bond_institution_name_2,
bond_holder_2,
bond_number_2,
bond_value_2,
bond_registration_date_2,
bond_type_2,
no_of_properties_in_bond_2,
bond_status_2,
bond_institution_name_3,
bond_holder_3,
bond_number_3,
bond_value_3,
bond_registration_date_3,
bond_type_3,
no_of_properties_in_bond_3,
bond_status_3,
property_rqg,
aivm_avm_estimate_value,
aivm_avm_flag,
rcv_ml_latest_estimated_value,
rcv_ml_confidence_score,
valdate,
aivm_avm_confidence_score,
aivm_avm_safety_score,
load_id,
load_date
from public.property;
grant select on public.vwproperty to valuations_readonly,valuations_readwrite;