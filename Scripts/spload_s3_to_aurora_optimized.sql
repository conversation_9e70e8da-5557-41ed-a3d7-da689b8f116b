CREATE OR REPLACE PROCEDURE public.spload_data_from_S3_v2(
    IN p_tablename TEXT,
    IN p_s3bucket TEXT,
    IN p_folderpath TEXT,
    IN p_loadtype TEXT,
    IN p_aws_region TEXT DEFAULT 'af-south-1',
    IN p_error_threshold NUMERIC DEFAULT 0.1,
    IN p_batch_size INT DEFAULT 1000
)
LANGUAGE plpgsql
AS $$
/*
===============================================================================
Procedure: spload_data_from_S3_v2 (Optimized Version)
===============================================================================
Improvements over v1:
- Added transaction management with proper rollback
- Enhanced security with table name validation
- Configurable parameters (region, error threshold, batch size)
- Better error handling and resource cleanup
- Optimized bulk processing with batching
- Reduced dynamic SQL complexity
===============================================================================
*/
DECLARE
    -- Configuration
    v_temp_table TEXT;
    v_error_table TEXT;
    v_load_id NUMERIC(100);
    v_file_name TEXT;
    
    -- Metadata cache
    v_column_list TEXT;
    v_column_names TEXT;
    v_casted_select TEXT;
    
    -- Counters
    v_rowcount NUMERIC(10) := 0;
    v_errorcount NUMERIC(10) := 0;
    v_processed_count NUMERIC(10) := 0;
    
    -- Processing variables
    v_batch_start INT := 1;
    v_batch_end INT;
    v_total_batches INT;
    
BEGIN
    -- Start transaction for data consistency
    BEGIN
        -- 1. SECURITY: Validate table exists and user has access
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_name = p_tablename
        ) THEN
            RAISE EXCEPTION 'Table % does not exist or access denied', p_tablename;
        END IF;
        
        -- 2. INITIALIZATION
        v_temp_table := p_tablename || '_temp_' || extract(epoch from now())::bigint;
        v_error_table := p_tablename || '_error';
        v_file_name := reverse(split_part(reverse(p_folderpath), '/', 1));
        v_load_id := extract(epoch from now())::bigint * 1000 + extract(milliseconds from now())::int;
        
        -- 3. DUPLICATE CHECK & CLEANUP
        PERFORM spload_handle_duplicates(p_tablename, v_file_name, v_load_id);
        
        -- 4. AUDIT LOG START
        INSERT INTO public.valuations_audit_log (
            file_name, table_name, source_s3_path, load_date, status, load_id
        ) VALUES (
            v_file_name, p_tablename, p_s3bucket || '/' || p_folderpath, 
            CURRENT_DATE, 'Running', v_load_id
        );
        
        -- 5. BUILD METADATA (cached for reuse)
        SELECT 
            string_agg(quote_ident(column_name) || ' VARCHAR', ', '),
            string_agg(quote_ident(column_name), ', '),
            string_agg(
                format('CAST(NULLIF(NULLIF(trim(%I), ''''), ''NULL'') AS %s)', 
                       column_name, data_type), ', '
            )
        INTO v_column_list, v_column_names, v_casted_select
        FROM information_schema.columns
        WHERE table_name = p_tablename 
          AND column_name NOT IN ('load_date', 'load_id')
        ORDER BY ordinal_position;
        
        -- 6. CREATE TEMP TABLE & LOAD FROM S3
        EXECUTE format('CREATE TEMP TABLE %I (%s)', v_temp_table, v_column_list);
        
        PERFORM aws_s3.table_import_from_s3(
            v_temp_table,
            '',
            '(format csv, header true, delimiter ''|'', quote ''"'', null '''')',
            p_s3bucket,
            p_folderpath,
            p_aws_region
        );
        
        -- 7. VALIDATE & COUNT
        GET DIAGNOSTICS v_rowcount = ROW_COUNT;
        IF v_rowcount = 0 THEN
            RAISE EXCEPTION 'No data loaded from S3 file: %', p_folderpath;
        END IF;
        
        -- 8. PREPARE TARGET TABLE
        IF p_loadtype = 'truncate' THEN
            EXECUTE format('TRUNCATE TABLE %I', p_tablename);
        END IF;
        
        -- 9. OPTIMIZED BULK INSERT WITH BATCHING
        v_total_batches := ceil(v_rowcount::NUMERIC / p_batch_size);
        
        FOR i IN 1..v_total_batches LOOP
            v_batch_start := (i - 1) * p_batch_size + 1;
            v_batch_end := LEAST(i * p_batch_size, v_rowcount);
            
            BEGIN
                EXECUTE format($sql$
                    INSERT INTO %I (%s, load_date, load_id)
                    SELECT %s, CURRENT_TIMESTAMP, %L
                    FROM (
                        SELECT *, ROW_NUMBER() OVER() as rn 
                        FROM %I
                    ) t 
                    WHERE rn BETWEEN %s AND %s
                $sql$, p_tablename, v_column_names, v_casted_select, 
                       v_load_id, v_temp_table, v_batch_start, v_batch_end);
                
                v_processed_count := v_processed_count + (v_batch_end - v_batch_start + 1);
                
                -- Progress reporting
                IF i % 10 = 0 OR i = v_total_batches THEN
                    RAISE INFO 'Processed batch % of % (% rows)', i, v_total_batches, v_processed_count;
                END IF;
                
            EXCEPTION WHEN OTHERS THEN
                -- Log batch error and continue
                v_errorcount := v_errorcount + (v_batch_end - v_batch_start + 1);
                
                INSERT INTO public.valuations_audit_log (
                    file_name, table_name, load_date, status, load_id
                ) VALUES (
                    v_file_name || '_batch_' || i, p_tablename, CURRENT_DATE, 
                    'Batch Error: ' || SQLERRM, v_load_id
                );
                
                -- Abort if error rate too high
                IF v_errorcount::NUMERIC / v_rowcount > p_error_threshold THEN
                    RAISE EXCEPTION 'Error rate %.2f%% exceeds threshold %.2f%%', 
                        (v_errorcount::NUMERIC / v_rowcount * 100), (p_error_threshold * 100);
                END IF;
            END;
        END LOOP;
        
        -- 10. FINAL AUDIT UPDATE
        UPDATE public.valuations_audit_log
        SET status = CASE WHEN v_errorcount > 0 THEN 'Completed with errors' ELSE 'Success' END,
            source_count = v_rowcount,
            processed_count = v_processed_count,
            error_count = v_errorcount
        WHERE load_id = v_load_id;
        
        -- 11. CLEANUP
        EXECUTE format('DROP TABLE IF EXISTS %I', v_temp_table);
        
        RAISE INFO 'Load completed: % rows processed, % errors', v_processed_count, v_errorcount;
        
    EXCEPTION WHEN OTHERS THEN
        -- Rollback transaction and cleanup
        ROLLBACK;
        
        -- Log failure
        UPDATE public.valuations_audit_log
        SET status = 'Failed: ' || SQLERRM,
            error_count = -1
        WHERE load_id = v_load_id;
        
        -- Cleanup temp table
        EXECUTE format('DROP TABLE IF EXISTS %I', v_temp_table);
        
        RAISE;
    END;
    
    -- Commit transaction
    COMMIT;
END;
$$;

-- Helper procedure for duplicate handling
CREATE OR REPLACE PROCEDURE spload_handle_duplicates(
    IN p_tablename TEXT,
    IN p_file_name TEXT,
    IN p_new_load_id NUMERIC
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_existing_load_id NUMERIC;
BEGIN
    SELECT load_id INTO v_existing_load_id
    FROM public.valuations_audit_log
    WHERE file_name = p_file_name 
      AND table_name = p_tablename 
      AND load_date = CURRENT_DATE 
      AND status = 'Success'
    ORDER BY load_id DESC 
    LIMIT 1;
    
    IF v_existing_load_id IS NOT NULL THEN
        RAISE INFO 'Removing duplicate load_id: %', v_existing_load_id;
        EXECUTE format('DELETE FROM %I WHERE load_id = %L', p_tablename, v_existing_load_id);
        
        UPDATE public.valuations_audit_log 
        SET status = 'Replaced by load_id: ' || p_new_load_id
        WHERE load_id = v_existing_load_id;
    END IF;
END;
$$;
