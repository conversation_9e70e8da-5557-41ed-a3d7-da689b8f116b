CREATE OR REPLACE PROCEDURE public.spload_data_from_S3_optimized(
    IN p_tablename TEXT,
    IN p_s3bucket TEXT,
    IN p_folderpath TEXT,
    IN p_loadtype TEXT
)
LANGUAGE plpgsql
AS $$
/*
===============================================================================
Procedure: spload_data_from_S3
===============================================================================
Purpose:
    Automates the process of loading CSV data from an AWS S3 bucket into a 
    PostgreSQL table. Supports two load types: 'truncate' (clears the table 
    before loading) and 'append' (adds new data to existing records).

Parameters:
    IN p_tablename   TEXT - Name of the target table.
    IN p_s3bucket    TEXT - Name of the S3 bucket containing the CSV file.
    IN p_folderpath  TEXT - Path to the folder or file within the S3 bucket.
    IN p_loadtype    TEXT - Type of load operation ('truncate' or 'append').

Main Steps:
    1. Validates the load type.
    2. Creates a temporary table with all columns as VARCHAR (excluding 'load_date').
    3. Loads CSV data from S3 into the temporary table using aws_s3.table_import_from_s3.
    4. Validates column count between temp and target tables.
    5. Truncates the target table if load type is 'truncate'.
    6. Creates an error logging table if it doesn't exist.
    7. Iterates through each row in the temp table:
        - Constructs and executes an INSERT statement.
        - Logs any failed inserts into the error table with full row data and error message.
    8. Logs the number of failed rows and completion status.

Error Handling:
    - Uses EXCEPTION WHEN OTHERS to catch and log errors during row insertion.
    - Failed rows are stored in <tablename>_error table for review.

Logging:
    - Uses RAISE INFO statements to provide progress updates and debug information.

Notes:
    - Assumes CSV files have headers and use comma as delimiter.
    - Region is hardcoded as 'af-south-1' for S3 access.
===============================================================================
*/
DECLARE
    -- Table names for temporary and error tables
    v_temp_table TEXT := p_tablename || '_temp';
    v_error_table TEXT := p_tablename || '_error';

    -- Column metadata for dynamic SQL construction
    v_column_list TEXT := '';        -- Column definitions for temp table creation
    v_column_names TEXT := '';       -- Column names for INSERT statements
    v_casted_select TEXT := '';      -- Type-casted SELECT clause for bulk insert
    v_loadquery TEXT;                -- Dynamic SQL for S3 import

    -- Column validation variables
    v_expected_columns INT;          -- Expected number of columns in target table
    v_file_columns INT;              -- Actual number of columns in CSV file
    v_column_diff TEXT;              -- Description of column count difference

    -- Row processing variables
    v_values TEXT;                   -- Formatted values for INSERT statement
    v_insert_sql TEXT;               -- Dynamic INSERT SQL statement
    record RECORD;                   -- Current row being processed

    -- Counters and statistics
    v_errorcount NUMERIC(10) := 0;   -- Number of failed row insertions
    v_rowcount NUMERIC(10) := 0;     -- Total number of rows in source file
    v_file_name TEXT;                -- Extracted filename from S3 path
    v_load_id numeric(100);          -- Timestamp-based load ID for tracking

    -- Progress tracking variables
	v_load_count numeric(100) :=0 ;  -- Number of successfully processed rows
	v_load_perc int :=0;             -- Current completion percentage
	v_last_reported_perc int := 0;   -- Last reported percentage milestone






    
BEGIN
    -- Extract file name from S3 path using reverse string manipulation
    -- This handles paths like 'folder/subfolder/filename.csv' to get 'filename.csv'
    v_file_name := reverse(split_part(reverse(p_folderpath), '/', 1));

	-- Check for duplicate processing: verify if this file was already successfully loaded today
	-- This prevents accidental reprocessing of the same file multiple times
	SELECT load_id INTO v_load_id
	FROM public.valuations_audit_log
	WHERE file_name = v_file_name
	  AND table_name = p_tablename
	  AND load_date = CURRENT_DATE
	  AND status = 'Success'
	ORDER BY load_id DESC
	LIMIT 1;

	-- If duplicate found, clean up previous load to avoid data duplication
	IF v_load_id is not null THEN
		RAISE INFO 'File % already processed today. Removing previous data with load_id %.',v_file_name,v_load_id;
		-- Remove previously loaded data from target table
		EXECUTE format('DELETE FROM %I WHERE load_id = %L;', p_tablename, v_load_id);
		-- Update audit log to indicate this load was replaced
		EXECUTE format('UPDATE public.valuations_audit_log set status = ''File reloaded. Check load_id entry for details'' WHERE load_id = %L and file_name = %L AND table_name = %L;', v_load_id, v_file_name, p_tablename);
	END IF;

	-- Generate unique load_id based on current timestamp (format: YYYYMMDDHH24MISS)
	-- This provides a sortable, unique identifier for each load operation
	v_load_id := cast(to_char(NOW(), 'YYYYMMDDHH24MISS') as numeric(100));


    -- Create audit trail: log the start of this load operation
    -- Status starts as 'Running' and will be updated upon completion
    INSERT INTO public.valuations_audit_log (
        file_name, table_name, source_s3_path, load_date, status, load_id
    ) VALUES (
        v_file_name, p_tablename, p_s3bucket || '/' || p_folderpath, CURRENT_DATE, 'Running', v_load_id
    );

    -- Input validation: ensure load type is one of the supported options
    IF p_loadtype NOT IN ('truncate', 'append') THEN
        RAISE EXCEPTION 'Invalid load type: %. Must be either "truncate" or "append".', p_loadtype;
    END IF;

    -- Build column metadata for temporary table creation
    -- Creates VARCHAR columns for all target table columns except system-generated ones
    -- This allows flexible data import before type casting during final insert
    SELECT string_agg(quote_ident(column_name) || ' VARCHAR', ', '),  -- Column definitions
           string_agg(quote_ident(column_name), ', ')                  -- Column names list
    INTO v_column_list, v_column_names
    FROM (
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = p_tablename
          AND column_name NOT IN ('load_date', 'load_id')  -- Exclude system columns
        ORDER BY ordinal_position                           -- Maintain column order
    ) t1;

    -- Create temporary staging table for raw CSV data import
    -- Drop any existing temp table to ensure clean state
    EXECUTE format('DROP TABLE IF EXISTS %I;', v_temp_table);
    -- Create new temp table with all VARCHAR columns for flexible data import
    EXECUTE format('CREATE TEMP TABLE %I (%s);', v_temp_table, v_column_list);

    -- Import CSV data from S3 using AWS extension
    -- Uses pipe delimiter, handles quoted fields, and treats empty strings as NULL
    v_loadquery := format($f$
        SELECT aws_s3.table_import_from_s3(
            '%I',                                                      -- Target temp table
            '',                                                        -- Column list (empty = all)
            '(format csv, header true, delimiter ''|'', quote ''"'', null '''')',  -- CSV options
            '%s',                                                      -- S3 bucket name
            '%s',                                                      -- S3 object path
            'af-south-1'                                              -- AWS region
        );
    $f$, v_temp_table, p_s3bucket, p_folderpath);

    EXECUTE v_loadquery;

    -- Get total row count from imported data for progress tracking and validation
    EXECUTE format('SELECT COUNT(*) FROM %I', v_temp_table) INTO STRICT v_rowcount;

    -- Validate schema compatibility between source file and target table
    -- Count expected columns in target table (excluding system-generated columns)
    SELECT COUNT(*) INTO v_expected_columns
    FROM information_schema.columns
    WHERE table_name = p_tablename
      AND column_name NOT IN ('load_date', 'load_id');

    -- Count actual columns in imported CSV data
    SELECT COUNT(*) INTO v_file_columns
    FROM information_schema.columns
    WHERE table_name = v_temp_table;

    -- Ensure column counts match to prevent data mapping issues
    IF v_expected_columns <> v_file_columns THEN
        v_column_diff := CASE
            WHEN v_file_columns > v_expected_columns THEN 'more'
            ELSE 'less'
        END;
        RAISE EXCEPTION 'Column mismatch: File has % columns than expected.', v_column_diff;
    END IF;

    -- Prepare target table based on load type
    -- For 'truncate' mode: clear all existing data before loading new data
    IF p_loadtype = 'truncate' THEN
        EXECUTE format('TRUNCATE TABLE %I;', p_tablename);
    END IF;
    -- Note: 'append' mode requires no preparation - new data is added to existing records

    -- Create error logging table for failed row insertions
    -- This table stores the full row data and error message for debugging
    EXECUTE format('
        CREATE TABLE IF NOT EXISTS public.%I (
            load_id NUMERIC(100),    -- Links errors to specific load operation
            fulldata TEXT,           -- Complete row data that failed to insert
            errormessage TEXT        -- PostgreSQL error message
        );',
        v_error_table
    );

    -- Build type-casting SELECT clause for bulk insert operation
    -- Converts VARCHAR temp table data to proper target table data types
    -- Handles NULL value variations ('NULL', '', 'null') consistently
    SELECT string_agg(
        format('CAST(case when %I in (''NULL'','''',''null'') then NULL else %I end AS %s)',
               column_name, column_name, data_type),
        ', '
    )
    INTO v_casted_select
    FROM (SELECT column_name, data_type
          FROM information_schema.columns
          WHERE table_name = p_tablename
            AND column_name NOT IN ('load_date', 'load_id')
          ORDER BY ordinal_position) t1;

    -- Attempt high-performance bulk insert first (preferred method)
    RAISE INFO 'Attempting bulk insert with casting';
	-- Debug SQL (commented): shows the exact INSERT statement being executed
	--RAISE INFO 'INSERT INTO % (%, load_date, load_id) SELECT %, CURRENT_TIMESTAMP, % FROM %;',p_tablename, v_column_names, v_casted_select, v_load_id, v_temp_table;

    -- Execute bulk insert with automatic type casting
    BEGIN
        EXECUTE format(
            'INSERT INTO %I (%s, load_date, load_id) SELECT %s, CURRENT_TIMESTAMP, %L FROM %I;',
            p_tablename, v_column_names, v_casted_select, v_load_id, v_temp_table
        );
    EXCEPTION WHEN OTHERS THEN
        -- Bulk insert failed - fall back to slower but more resilient row-by-row processing
        -- This allows individual row errors to be captured while continuing with valid rows
        RAISE INFO 'Bulk insert with casting failed. Falling back to row-by-row insert. Error: %', SQLERRM;

        -- Process each row individually with error handling
        FOR record IN EXECUTE format('SELECT * FROM %I', v_temp_table)
        LOOP
            BEGIN
                -- Build VALUES clause with proper NULL handling and SQL escaping
                SELECT string_agg(
                    CASE
                        WHEN (row_to_json(record) ->> col) IS NULL
                             OR trim(row_to_json(record) ->> col) = ''
                             OR upper(trim(row_to_json(record) ->> col)) = 'NULL'
                        THEN 'NULL'                                    -- Use SQL NULL
                        ELSE quote_literal(trim(row_to_json(record) ->> col))  -- Escape and quote strings
                    END,
                    ', '
                )
                INTO v_values
                FROM unnest(string_to_array(v_column_names, ', ')) AS col;

                -- Construct and execute individual INSERT statement
                v_insert_sql := format(
                    'INSERT INTO %I (%s, load_date, load_id) VALUES (%s, CURRENT_TIMESTAMP, %L);',
                    p_tablename, v_column_names, v_values, v_load_id
                );
                EXECUTE v_insert_sql;


				-- Track progress and provide periodic status updates
				v_load_count := v_load_count + 1;
				v_load_perc := floor((v_load_count::NUMERIC / v_rowcount) * 100);

				-- Report progress at 10% intervals to avoid log spam
				IF v_load_perc >= v_last_reported_perc + 10 THEN
					RAISE INFO '% records loaded', v_load_perc||'%';
					v_last_reported_perc := v_load_perc - (v_load_perc % 10);
				END IF;

            EXCEPTION WHEN OTHERS THEN
                -- Log individual row failures for debugging while continuing processing
                v_errorcount := v_errorcount + 1;
                EXECUTE format('
                    INSERT INTO public.%I (load_id, fulldata, errormessage)
                    VALUES (%L, %L, %L);',
                    v_error_table,
                    v_load_id,
					row_to_json(record)::TEXT,  -- Store complete row data for analysis
                    SQLERRM                     -- PostgreSQL error message
                );

                -- Safety mechanism: abort if error rate becomes too high (>10%)
                -- This prevents runaway processes with fundamentally bad data
                IF v_errorcount > (v_rowcount * 0.1) THEN
                    RAISE INFO 'Aborting loop: error count % exceeds 10%% of % rows.', v_errorcount, v_rowcount;
                    EXIT;
                END IF;
            END;
        END LOOP;
    END;

    -- Update audit log with final load statistics and status
    UPDATE public.valuations_audit_log
    SET
        status = CASE WHEN v_errorcount > 0 THEN 'Failed' ELSE 'Success' END,
        source_count = v_rowcount,                    -- Total rows in source file
        processed_count = v_rowcount - v_errorcount,  -- Successfully inserted rows
        error_count = v_errorcount                    -- Failed row count
    WHERE file_name = v_file_name AND table_name = p_tablename AND load_id = v_load_id;

    -- Provide final status summary to caller
    IF v_errorcount > 0 THEN
        RAISE INFO 'Load complete with % error(s) out of % rows. Audit log updated.', v_errorcount, v_rowcount;
	ELSE
	    RAISE INFO 'Load complete. Audit log updated.';
    END IF;

END;
$$;
