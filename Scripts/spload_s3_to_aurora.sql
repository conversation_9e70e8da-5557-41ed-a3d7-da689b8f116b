CREATE OR REPLACE PROCEDURE public.spload_data_from_S3(
    IN p_tablename TEXT,
    IN p_s3bucket TEXT,
    IN p_folderpath TEXT,
    IN p_loadtype TEXT
)
LANGUAGE plpgsql
AS $$
/*
===============================================================================
Procedure: spload_data_from_S3
===============================================================================
Purpose:
    Automates the process of loading CSV data from an AWS S3 bucket into a 
    PostgreSQL table. Supports two load types: 'truncate' (clears the table 
    before loading) and 'append' (adds new data to existing records).

Parameters:
    IN p_tablename   TEXT - Name of the target table.
    IN p_s3bucket    TEXT - Name of the S3 bucket containing the CSV file.
    IN p_folderpath  TEXT - Path to the folder or file within the S3 bucket.
    IN p_loadtype    TEXT - Type of load operation ('truncate' or 'append').

Main Steps:
    1. Validates the load type.
    2. Creates a temporary table with all columns as VARCHAR (excluding 'load_date').
    3. Loads CSV data from S3 into the temporary table using aws_s3.table_import_from_s3.
    4. Validates column count between temp and target tables.
    5. Truncates the target table if load type is 'truncate'.
    6. Creates an error logging table if it doesn't exist.
    7. Iterates through each row in the temp table:
        - Constructs and executes an INSERT statement.
        - Logs any failed inserts into the error table with full row data and error message.
    8. Logs the number of failed rows and completion status.

Error Handling:
    - Uses EXCEPTION WHEN OTHERS to catch and log errors during row insertion.
    - Failed rows are stored in <tablename>_error table for review.

Logging:
    - Uses RAISE INFO statements to provide progress updates and debug information.

Notes:
    - Assumes CSV files have headers and use comma as delimiter.
    - Region is hardcoded as 'af-south-1' for S3 access.
===============================================================================
*/
DECLARE
    v_temp_table TEXT := p_tablename || '_temp';
    v_error_table TEXT := p_tablename || '_error';
    v_column_list TEXT := '';
    v_column_names TEXT := '';
    v_casted_select TEXT := '';
    v_loadquery TEXT;
    v_expected_columns INT;
    v_file_columns INT;
    v_column_diff TEXT;
    v_values TEXT;
    v_insert_sql TEXT;
    record RECORD;
    v_errorcount NUMERIC(10) := 0;
    v_rowcount NUMERIC(10) := 0;
    v_file_name TEXT;
    v_load_id numeric(100);   -- Timestamp-based load ID
	v_load_count numeric(100) :=0 ;
	v_load_perc int :=0;
	v_last_reported_perc int := 0;
BEGIN
    -- Extract file name from S3 path
    v_file_name := reverse(split_part(reverse(p_folderpath), '/', 1));

	
	-- Check if file has already been processed today
	SELECT load_id INTO v_load_id
	FROM public.valuations_audit_log
	WHERE file_name = v_file_name AND table_name = p_tablename AND load_date = CURRENT_DATE AND status = 'Success' order by load_id desc LIMIT 1;
	
	-- If found, delete previously loaded data
	IF v_load_id is not null THEN
		RAISE INFO 'File % already processed today. Removing previous data with load_id %.',v_file_name,v_load_id;
		EXECUTE format('DELETE FROM %I WHERE load_id = %L;', p_tablename, v_load_id);
		EXECUTE format('UPDATE public.valuations_audit_log set status = ''File reloaded. Check load_id entry for details'' WHERE load_id = %L;', v_load_id);
	END IF;
	
	-- Generate new load_id for this run
	v_load_id := cast(to_char(NOW(), 'YYYYMMDDHH24MISS') as numeric(100));

	
    -- Insert initial audit log with status 'Running'
    INSERT INTO public.valuations_audit_log (
        file_name, table_name, source_s3_path, load_date, status, load_id
    ) VALUES (
        v_file_name, p_tablename, p_s3bucket || '/' || p_folderpath, CURRENT_DATE, 'Running', v_load_id
    );

    -- Validate load type
    IF p_loadtype NOT IN ('truncate', 'append') THEN
        RAISE EXCEPTION 'Invalid load type: %. Must be either "truncate" or "append".', p_loadtype;
    END IF;

    -- Create temp table with all columns as VARCHAR except load_date/load_id
    SELECT string_agg(quote_ident(column_name) || ' VARCHAR', ', '),
           string_agg(quote_ident(column_name), ', ')
    INTO v_column_list, v_column_names
    FROM (
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = p_tablename
          AND column_name NOT IN ('load_date', 'load_id')
        ORDER BY ordinal_position
    ) t1;
	
    
    EXECUTE format('DROP TABLE IF EXISTS %I;', v_temp_table);
    EXECUTE format('CREATE TEMP TABLE %I (%s);', v_temp_table, v_column_list);

    -- Load data from S3
    v_loadquery := format($f$
        SELECT aws_s3.table_import_from_s3(
            '%I',
            '',
            '(format csv, header true, delimiter ''|'', quote ''"'', null '''')',
            '%s',
            '%s',
            'af-south-1'
        );
    $f$, v_temp_table, p_s3bucket, p_folderpath);

    EXECUTE v_loadquery;

    -- Count rows loaded
    EXECUTE format('SELECT COUNT(*) FROM %I', v_temp_table) INTO STRICT v_rowcount;

    -- Compare column count
    SELECT COUNT(*) INTO v_expected_columns
    FROM information_schema.columns
    WHERE table_name = p_tablename
      AND column_name NOT IN ('load_date', 'load_id');

    SELECT COUNT(*) INTO v_file_columns
    FROM information_schema.columns
    WHERE table_name = v_temp_table;

    IF v_expected_columns <> v_file_columns THEN
        v_column_diff := CASE
            WHEN v_file_columns > v_expected_columns THEN 'more'
            ELSE 'less'
        END;
        RAISE EXCEPTION 'Column mismatch: File has % columns than expected.', v_column_diff;
    END IF;

    -- Truncate target table if needed
    IF p_loadtype = 'truncate' THEN
        EXECUTE format('TRUNCATE TABLE %I;', p_tablename);
    END IF;

    -- Create error table if not exists
    EXECUTE format('
        CREATE TABLE IF NOT EXISTS public.%I (
            fulldata TEXT,
            errormessage TEXT
        );',
        v_error_table
    );

    -- Build casted select clause for bulk insert
    SELECT string_agg(
        format('CAST(case when %I in (''NULL'','''',''null'') then NULL else %I end AS %s)', column_name,column_name, data_type),
        ', '
    )
    INTO v_casted_select
    FROM (select column_name,data_type from information_schema.columns
    WHERE table_name = p_tablename
      AND column_name NOT IN ('load_date', 'load_id') order by ordinal_position) t1;

	raise info 'A';

    -- Attempt bulk insert with casting
    BEGIN
        EXECUTE format(
            'INSERT INTO %I (%s, load_date, load_id) SELECT %s, CURRENT_TIMESTAMP, %L FROM %I;',
            p_tablename, v_column_names, v_casted_select, v_load_id, v_temp_table
        );
    EXCEPTION WHEN OTHERS THEN
        RAISE INFO 'Bulk insert with casting failed. Falling back to row-by-row insert. Error: %', SQLERRM;

        -- Row-by-row fallback
        FOR record IN EXECUTE format('SELECT * FROM %I', v_temp_table)
        LOOP
            BEGIN
                SELECT string_agg(
                    CASE
                        WHEN (row_to_json(record) ->> col) IS NULL
                             OR trim(row_to_json(record) ->> col) = ''
                             OR upper(trim(row_to_json(record) ->> col)) = 'NULL'
                        THEN 'NULL'
                        ELSE quote_literal(trim(row_to_json(record) ->> col))
                    END,
                    ', '
                )
                INTO v_values
                FROM unnest(string_to_array(v_column_names, ', ')) AS col;

                v_insert_sql := format(
                    'INSERT INTO %I (%s, load_date, load_id) VALUES (%s, CURRENT_TIMESTAMP, %L);',
                    p_tablename, v_column_names, v_values, v_load_id
                );
                EXECUTE v_insert_sql;


				-- after each successful insert:
				v_load_count := v_load_count + 1;
				v_load_perc := floor((v_load_count::NUMERIC / v_rowcount) * 100);
								
				-- Only raise info when we hit a new 10% milestone
				if v_load_perc >= v_last_reported_perc + 10 then
					raise INFO '% perc records loaded', v_load_perc||'%';
					v_last_reported_perc := v_load_perc - (v_load_perc % 10);
				end if;

            EXCEPTION WHEN OTHERS THEN
                v_errorcount := v_errorcount + 1;
                EXECUTE format('
                    INSERT INTO public.%I (load_id,fulldata, errormessage)
                    VALUES (%L, %L, %L);',
                    v_error_table,
                    v_load_id,
					row_to_json(record)::TEXT,
                    SQLERRM
                );

                -- Abort loop if error count exceeds 10%
                IF v_errorcount > (v_rowcount * 0.1) THEN
                    RAISE INFO 'Aborting loop: error count % exceeds 10%% of % rows.', v_errorcount, v_rowcount;
                    EXIT;
                END IF;
            END;
        END LOOP;
    END;

    -- Final audit update
    UPDATE public.valuations_audit_log
    SET
        status = CASE WHEN v_errorcount > 0 THEN 'Failed' ELSE 'Success' END,
        source_count = v_rowcount,
        processed_count = v_rowcount - v_errorcount,
        error_count = v_errorcount
    WHERE load_id = v_load_id;

    -- Abort procedure if any error occurred
    if v_errorcount > 0 then
        raise info 'Load complete with % error(s) out of % rows. Audit log updated.', v_errorcount, v_rowcount;
	else
	    raise info 'Load complete. Audit log updated.';
    end if;

end;
$$;
